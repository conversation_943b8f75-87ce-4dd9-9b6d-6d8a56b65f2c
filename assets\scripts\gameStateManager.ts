import { _decorator, Node } from 'cc';
import { cellFixed } from './cellFixed';
import { AnimationStateManager } from './animationStateManager';

/**
 * 游戏状态管理器 - 负责管理选中状态、失败反馈等游戏状态
 */
export class GameStateManager {

    /** 当前选中的棋子节点 */
    private selectedCell: Node = null;

    /** 失败反馈的延迟操作ID */
    private failureTimeoutId: number = null;

    /** 处于失败反馈状态的棋子列表 */
    private failureFeedbackCells: Node[] = [];

    /** 动画状态管理器 */
    private animationManager: AnimationStateManager;

    constructor() {
        this.animationManager = AnimationStateManager.getInstance();
    }

    /**
     * 选中棋子
     * @param cellNode 要选中的棋子节点
     */
    public selectCell(cellNode: Node): void {
        this.selectedCell = cellNode;
        this.animationManager.setSelected(cellNode);
    }
    
    /**
     * 取消选中棋子
     */
    public unselectCell(): void {
        if (this.selectedCell) {
            this.animationManager.setNormal(this.selectedCell);
            this.selectedCell = null;
        }

        // 同时清理任何失败反馈状态
        this.cancelFailureTimeout();
    }
    
    /**
     * 获取当前选中的棋子
     */
    public getSelectedCell(): Node {
        return this.selectedCell;
    }
    
    /**
     * 检查是否有选中的棋子
     */
    public hasSelectedCell(): boolean {
        return this.selectedCell !== null;
    }
    
    /**
     * 检查是否选中了指定棋子
     * @param cellNode 要检查的棋子节点
     */
    public isSelected(cellNode: Node): boolean {
        return this.selectedCell === cellNode;
    }
    
    /**
     * 清除选中状态（不播放动画）
     */
    public clearSelection(): void {
        this.selectedCell = null;
    }
    
    /**
     * 显示连接失败反馈
     * @param cell1 第一个棋子
     * @param cell2 第二个棋子
     */
    public showConnectionFailure(cell1: Node, cell2: Node): void {
        // 清理之前的失败状态
        this.clearPreviousFailureState();

        // 添加到失败反馈列表
        this.failureFeedbackCells = [cell1, cell2];

        // 播放失败动画
        this.animationManager.playFailureAnimation(cell1);
        this.animationManager.playFailureAnimation(cell2);

        // 设置延迟恢复
        this.failureTimeoutId = window.setTimeout(() => {
            this.resetAllSelections(cell1, cell2);
            this.failureTimeoutId = null;
        }, 1000);
    }
    
    /**
     * 重置所有选中状态
     * @param cell1 第一个棋子节点
     * @param cell2 第二个棋子节点
     */
    public resetAllSelections(cell1: Node, cell2: Node): void {
        // 重置两个棋子到正常状态
        this.animationManager.setNormal(cell1);
        this.animationManager.setNormal(cell2);

        // 清除选中状态
        this.selectedCell = null;
    }
    
    /**
     * 清理之前的失败状态
     */
    public clearPreviousFailureState(): void {
        this.cancelFailureTimeout();
        this.clearFailureFeedbackAnimations();
    }
    
    /**
     * 取消失败反馈延迟操作
     */
    private cancelFailureTimeout(): void {
        if (this.failureTimeoutId !== null) {
            clearTimeout(this.failureTimeoutId);
            this.failureTimeoutId = null;
        }
    }
    
    /**
     * 清理失败反馈动画状态
     */
    private clearFailureFeedbackAnimations(): void {
        if (this.failureFeedbackCells.length > 0) {
            // 使用动画管理器强制重置
            this.animationManager.forceResetMultiple(this.failureFeedbackCells);

            // 清空失败反馈棋子列表
            this.failureFeedbackCells = [];
        }
    }
    
    /**
     * 检查两个棋子类型是否匹配
     * @param cell1 第一个棋子
     * @param cell2 第二个棋子
     */
    public isTypeMatching(cell1: Node, cell2: Node): boolean {
        const type1 = cell1.getComponent(cellFixed)?.getType();
        const type2 = cell2.getComponent(cellFixed)?.getType();
        
        return type1 === type2;
    }
    
    /**
     * 检查游戏是否胜利（所有棋子都已消除）
     * @param gridNode 网格节点
     */
    public checkGameWin(gridNode: Node): boolean {
        if (!gridNode) {
            return false;
        }
        
        // 统计还有多少活跃的棋子
        let activeCellCount = 0;
        gridNode.children.forEach(cellNode => {
            if (cellNode.active) {
                activeCellCount++;
            }
        });

        return activeCellCount === 0;
    }
    
    /**
     * 消除两个棋子
     * @param cell1 第一个棋子
     * @param cell2 第二个棋子
     * @param callback 消除完成后的回调
     */
    public destroyCells(cell1: Node, cell2: Node, callback?: () => void): void {
        if (!cell1 || !cell2) {
            console.error("棋子为空，无法消除");
            callback?.();
            return;
        }

        // 清除选中状态
        this.selectedCell = null;

        // 播放消除动画
        let completedCount = 0;
        const onAnimationComplete = () => {
            completedCount++;
            if (completedCount === 2) {
                callback?.();
            }
        };

        this.animationManager.playDestroyAnimation(cell1, onAnimationComplete);
        this.animationManager.playDestroyAnimation(cell2, onAnimationComplete);
    }
    
    /**
     * 获取状态信息（调试用）
     */
    public getStateInfo(): object {
        return {
            hasSelectedCell: this.hasSelectedCell(),
            selectedCellType: this.selectedCell ? this.selectedCell.getComponent(cellFixed)?.getType() : null,
            failureFeedbackCount: this.failureFeedbackCells.length,
            hasFailureTimeout: this.failureTimeoutId !== null
        };
    }
    
    /**
     * 打印状态信息（调试用）
     */
    public printStateInfo(): void {
        const info = this.getStateInfo();
        console.log("=== 游戏状态信息 ===", info);
    }
}
