import { _decorator, Component, Node, Label, Button } from 'cc';
const { ccclass, property } = _decorator;
import { AnimationStateManager, AnimationState } from './animationStateManager';

/**
 * 失败动画Bug测试脚本
 * 专门测试快速点击时失败动画被打断的问题
 */
@ccclass('failureAnimationBugTest')
export class failureAnimationBugTest extends Component {
    
    @property({ type: Label, displayName: "测试状态标签" })
    statusLabel: Label = null!;
    
    @property({ type: Node, displayName: "测试按钮容器" })
    buttonContainer: Node = null!;
    
    @property({ type: [Node], displayName: "测试棋子节点" })
    testCells: Node[] = [];
    
    private animationManager: AnimationStateManager = null!;
    private testStep: number = 0;
    
    onLoad() {
        this.animationManager = AnimationStateManager.getInstance();
        this.createTestButtons();
        this.updateStatus("失败动画Bug测试准备就绪");
    }
    
    /**
     * 创建测试按钮
     */
    private createTestButtons() {
        if (!this.buttonContainer) {
            console.warn("按钮容器未设置");
            return;
        }
        
        this.createButton("模拟失败动画Bug", () => this.simulateFailureAnimationBug());
        this.createButton("测试异常状态清理", () => this.testAbnormalStateCleanup());
        this.createButton("测试快速点击序列", () => this.testRapidClickSequence());
        this.createButton("检查棋子状态", () => this.checkCellStates());
        this.createButton("强制重置所有", () => this.forceResetAll());
    }
    
    /**
     * 创建单个测试按钮
     */
    private createButton(text: string, callback: () => void) {
        const buttonNode = new Node(text);
        buttonNode.parent = this.buttonContainer;
        
        const button = buttonNode.addComponent(Button);
        const label = buttonNode.addComponent(Label);
        
        label.string = text;
        label.fontSize = 16;
        
        button.node.on(Button.EventType.CLICK, callback, this);
    }
    
    /**
     * 更新状态显示
     */
    private updateStatus(message: string) {
        if (this.statusLabel) {
            this.statusLabel.string = `步骤 ${this.testStep}: ${message}`;
        }
    }
    
    /**
     * 模拟失败动画Bug场景
     */
    private simulateFailureAnimationBug() {
        if (this.testCells.length < 4) {
            this.updateStatus("❌ 需要至少4个测试棋子节点");
            return;
        }
        
        this.testStep = 1;
        this.updateStatus("开始模拟失败动画Bug");
        
        const [cell1, cell2, cell3, cell4] = this.testCells;
        
        // 步骤1：选中两个棋子并触发失败动画
        this.animationManager.setSelected(cell1);
        this.animationManager.setSelected(cell2);
        
        this.scheduleOnce(() => {
            this.testStep = 2;
            this.updateStatus("触发失败动画");
            
            // 触发失败动画
            this.animationManager.playFailureAnimation(cell1);
            this.animationManager.playFailureAnimation(cell2);
            
            // 步骤2：在失败动画过程中快速点击其他棋子
            this.scheduleOnce(() => {
                this.testStep = 3;
                this.updateStatus("快速点击其他棋子（模拟打断）");
                
                // 清理异常状态（模拟新点击的清理逻辑）
                this.animationManager.clearAbnormalStates();
                
                // 选中新的棋子
                this.animationManager.setSelected(cell3);
                this.animationManager.setSelected(cell4);
                
                // 检查结果
                this.scheduleOnce(() => {
                    this.checkBugFixResult();
                }, 0.5);
                
            }, 0.2); // 在失败动画进行中打断
            
        }, 0.5);
    }
    
    /**
     * 检查Bug修复结果
     */
    private checkBugFixResult() {
        this.testStep = 4;
        this.updateStatus("检查Bug修复结果");
        
        const [cell1, cell2, cell3, cell4] = this.testCells;
        
        // 检查前两个棋子是否正确重置
        const cell1State = this.animationManager.getCurrentState(cell1);
        const cell2State = this.animationManager.getCurrentState(cell2);
        const cell3State = this.animationManager.getCurrentState(cell3);
        const cell4State = this.animationManager.getCurrentState(cell4);
        
        // Bug修复结果检查（简化输出）
        console.log("Bug修复检查:", {
            cell1: { state: cell1State, scale: cell1.scale.x },
            cell2: { state: cell2State, scale: cell2.scale.x },
            cell3: { state: cell3State, scale: cell3.scale.x },
            cell4: { state: cell4State, scale: cell4.scale.x }
        });
        
        // 验证修复效果
        const isCell1Fixed = cell1State === AnimationState.NORMAL && cell1.scale.x === 1.0;
        const isCell2Fixed = cell2State === AnimationState.NORMAL && cell2.scale.x === 1.0;
        const isCell3Selected = cell3State === AnimationState.SELECTED && cell3.scale.x === 1.1;
        const isCell4Selected = cell4State === AnimationState.SELECTED && cell4.scale.x === 1.1;
        
        if (isCell1Fixed && isCell2Fixed && isCell3Selected && isCell4Selected) {
            this.updateStatus("✅ Bug修复成功！前面的棋子正确重置");
        } else {
            this.updateStatus("❌ Bug仍存在，状态不正确");
        }
        

    }
    
    /**
     * 测试异常状态清理
     */
    private testAbnormalStateCleanup() {
        this.updateStatus("测试异常状态清理功能");
        
        if (this.testCells.length < 2) {
            this.updateStatus("❌ 需要至少2个测试棋子节点");
            return;
        }
        
        const [cell1, cell2] = this.testCells;
        
        // 设置一些异常状态
        this.animationManager.playFailureAnimation(cell1);
        this.animationManager.setSelected(cell2);
        
        this.scheduleOnce(() => {
            // 清理异常状态
            this.animationManager.clearAbnormalStates();
            
            // 检查结果
            this.scheduleOnce(() => {
                const cell1State = this.animationManager.getCurrentState(cell1);
                const cell2State = this.animationManager.getCurrentState(cell2);
                
                console.log(`清理后状态: Cell1=${cell1State}, Cell2=${cell2State}`);
                
                if (cell1State === AnimationState.NORMAL && cell2State === AnimationState.SELECTED) {
                    this.updateStatus("✅ 异常状态清理成功");
                } else {
                    this.updateStatus("❌ 异常状态清理失败");
                }
            }, 0.3);
        }, 0.2);
    }
    
    /**
     * 测试快速点击序列
     */
    private testRapidClickSequence() {
        this.updateStatus("测试快速点击序列");
        
        if (this.testCells.length < 3) {
            this.updateStatus("❌ 需要至少3个测试棋子节点");
            return;
        }
        
        const [cell1, cell2, cell3] = this.testCells;
        
        // 快速连续操作
        this.animationManager.setSelected(cell1);
        
        this.scheduleOnce(() => {
            this.animationManager.playFailureAnimation(cell1);
        }, 0.1);
        
        this.scheduleOnce(() => {
            this.animationManager.clearAbnormalStates();
            this.animationManager.setSelected(cell2);
        }, 0.2);
        
        this.scheduleOnce(() => {
            this.animationManager.setSelected(cell3);
        }, 0.3);
        
        this.scheduleOnce(() => {
            this.updateStatus("快速点击序列完成，检查状态");
            this.checkCellStates();
        }, 0.5);
    }
    
    /**
     * 检查所有棋子状态
     */
    private checkCellStates() {
        this.updateStatus("检查所有棋子状态");
        
        // 棋子状态检查（简化输出）
        const states = this.testCells.map((cell, index) => {
            if (cell) {
                return {
                    cell: index + 1,
                    state: this.animationManager.getCurrentState(cell),
                    animating: this.animationManager.isAnimating(cell),
                    scale: cell.scale.x
                };
            }
            return null;
        }).filter(Boolean);

        console.log("棋子状态:", states);
        console.log("动画管理器:", this.animationManager.getDebugInfo());
    }
    
    /**
     * 强制重置所有棋子
     */
    private forceResetAll() {
        this.updateStatus("强制重置所有棋子");
        
        this.testCells.forEach(cell => {
            if (cell) {
                this.animationManager.forceResetToNormal(cell);
            }
        });
        
        this.scheduleOnce(() => {
            this.updateStatus("所有棋子已重置");
            this.checkCellStates();
        }, 0.2);
    }
}
