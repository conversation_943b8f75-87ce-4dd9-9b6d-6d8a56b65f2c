import { _decorator, Component, Button, input, Input, EventKeyboard, KeyCode } from 'cc';
const { ccclass } = _decorator;
import { AudioManager } from './audioManager';
import { VibrationSystem } from './vibrationSystem';
import { gameMgrSimplified } from './gameMgrSimplified';

/**
 * Loading界面控制器
 * 简单的loading界面，点击按钮后切换到游戏主界面
 */
@ccclass('loading')
export class loading extends Component {

    private startButton: Button = null!;

    onLoad() {
        // 自动查找并绑定开始按钮
        this.findStartButton();
    }

    /**
     * 自动查找开始按钮
     */
    private findStartButton() {


        // 方法1：通过节点名称查找（更多可能的名称）
        const possibleNames = [
            'StartButton', 'startButton', 'StartBtn', 'startBtn',
            '开始游戏', '开始', 'enterGame', 'EnterGame',
            'start', 'Start', 'begin', 'Begin'
        ];

        for (const name of possibleNames) {
            const buttonNode = this.node.getChildByName(name);
            if (buttonNode) {
                const button = buttonNode.getComponent(Button);
                if (button) {
                    this.startButton = button;
                    button.node.on('click', this.startBtn, this);
                    return;
                }
            }
        }

        // 方法2：递归查找所有子节点中的Button组件
        const buttons = this.node.getComponentsInChildren(Button);

        if (buttons.length > 0) {
            this.startButton = buttons[0];
            buttons[0].node.on('click', this.startBtn, this);
        } else {
            console.error("未找到任何按钮组件");
        }
    }

    start() {
        // 播放loading音乐
        const audioManager = AudioManager.getInstance();
        if (audioManager) {
            audioManager.playLoadingMusic();
        }

        // 添加全局点击监听（备用方案）
        this.node.on('click', this.onNodeClick, this);

        // 添加键盘监听（备用方案）
        this.addKeyboardListener();
    }

    /**
     * 添加键盘监听
     */
    private addKeyboardListener() {
        input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);
    }

    /**
     * 键盘按下事件
     */
    private onKeyDown(event: EventKeyboard) {
        if (event.keyCode === KeyCode.SPACE || event.keyCode === KeyCode.ENTER) {
            this.startBtn();
        }
    }

    /**
     * 节点点击事件（备用方案）
     */
    private onNodeClick() {
        this.startBtn();
    }

    onDestroy() {
        // 清理事件监听
        input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);
    }

    /**
     * 开始游戏按钮点击事件
     */
    public startBtn() {


        // 立即播放点击音效和震动
        const audioManager = AudioManager.getInstance();
        const vibrationSystem = VibrationSystem.getInstance();

        if (audioManager) {
            audioManager.playClickSound();
            // 立即停止loading音乐，准备切换到游戏音乐
            audioManager.stopBGM();
        }

        if (vibrationSystem) {
            vibrationSystem.vibrate([50]);
        }

        // 获取游戏管理器
        const gameManager = gameMgrSimplified.getInstance();
        if (!gameManager) {
            console.error("游戏管理器未找到");
            return;
        }

        // 优化的切换流程：分帧执行，避免卡顿
        this.performOptimizedTransition(gameManager);
    }

    /**
     * 执行优化的界面切换
     */
    private performOptimizedTransition(gameManager: gameMgrSimplified) {
        // 立即开始游戏音频切换
        const audioManager = AudioManager.getInstance();
        if (audioManager) {
            audioManager.onGameStart();
        }

        // 立即加载游戏界面
        gameManager.enterGameOptimized();

        // 短暂延迟后销毁loading界面
        this.scheduleOnce(() => {
            this.node.destroy();
        }, 0.1);
    }

    /**
     * 备用按钮点击方法（如果startBtn不工作，可以尝试这个）
     */
    public onStartButtonClick() {
        this.startBtn();
    }

    /**
     * 另一个备用方法
     */
    public startGame() {
        this.startBtn();
    }
}


