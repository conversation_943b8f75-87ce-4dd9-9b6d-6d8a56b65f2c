<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日志管理器测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .config-section {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .config-item {
            margin: 5px 0;
        }
        #console-output {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 日志管理器测试</h1>
        
        <div class="config-section">
            <h3>📋 当前日志配置（简化版）</h3>
            <p style="color: #666; font-size: 14px; margin-bottom: 10px;">
                ✅ 只保留重要日志：无解检测、Toast提示、加载状态、错误信息<br>
                ❌ 已禁用噪音日志：游戏管理器、连接系统、选择逻辑等高频输出
            </p>
            <div id="config-display"></div>
            <button onclick="updateConfigDisplay()">刷新配置</button>
            <button onclick="enableDebugMode()">启用调试模式</button>
            <button onclick="enableProductionMode()">生产模式</button>
        </div>

        <div class="test-section">
            <h3>🎮 游戏管理器日志测试</h3>
            <button onclick="testGameManager()">测试游戏管理器日志</button>
        </div>

        <div class="test-section">
            <h3>🔗 连接系统日志测试</h3>
            <button onclick="testConnectionSystem()">测试连接系统日志</button>
        </div>

        <div class="test-section">
            <h3>👆 选择逻辑日志测试</h3>
            <button onclick="testSelectionLogic()">测试选择逻辑日志</button>
        </div>

        <div class="test-section">
            <h3>🎵 音频管理器日志测试</h3>
            <button onclick="testAudioManager()">测试音频管理器日志</button>
        </div>

        <div class="test-section">
            <h3>🔄 洗牌算法日志测试</h3>
            <button onclick="testShuffleAlgorithm()">测试洗牌算法日志</button>
        </div>

        <div class="test-section">
            <h3>🎬 动画状态日志测试</h3>
            <button onclick="testAnimationState()">测试动画状态日志</button>
        </div>

        <div class="test-section">
            <h3>📱 菜单管理器日志测试</h3>
            <button onclick="testMenuManager()">测试菜单管理器日志</button>
        </div>

        <div class="test-section">
            <h3>🍞 Toast系统日志测试</h3>
            <button onclick="testToastSystem()">测试Toast系统日志</button>
        </div>

        <div class="test-section">
            <h3>📦 加载界面日志测试</h3>
            <button onclick="testLoading()">测试加载界面日志</button>
        </div>

        <div class="test-section">
            <h3>🎯 棋子组件日志测试</h3>
            <button onclick="testCellComponent()">测试棋子组件日志</button>
        </div>

        <div class="test-section">
            <h3>🧪 测试模块日志测试</h3>
            <button onclick="testTestingModule()">测试测试模块日志</button>
        </div>

        <div class="test-section">
            <h3>📳 振动系统日志测试</h3>
            <button onclick="testVibration()">测试振动系统日志</button>
        </div>

        <div class="test-section">
            <h3>🔧 配置切换测试</h3>
            <button onclick="toggleAllLogs()">切换所有日志</button>
            <button onclick="toggleErrorLogs()">切换错误日志</button>
            <button onclick="clearConsole()">清空控制台</button>
        </div>

        <div class="test-section">
            <h3>📺 控制台输出</h3>
            <div id="console-output"></div>
        </div>
    </div>

    <script>
        // 模拟LogConfig和LogManager（简化版配置）
        const LogConfig = {
            // 启用的重要日志
            deadlockDetector: true,     // 无解检测日志（重要）
            toastSystem: true,          // Toast提示系统日志（用户可见）
            loading: true,              // 加载界面日志（重要状态）
            enableErrorLogs: true,      // 错误日志（必须）

            // 禁用的日志（减少噪音）
            gameManager: false,         // 游戏管理器日志（输出太多）
            connectionSystem: false,    // 连接系统日志（输出太多）
            selectionLogic: false,      // 选择逻辑日志（输出太多）
            animationState: false,      // 动画状态日志（输出太多）
            shuffleAlgorithm: false,    // 洗牌算法日志（太详细）
            menuManager: false,         // 菜单管理器日志（减少输出）
            audioManager: false,        // 音频管理器日志（不重要）
            cellComponent: false,       // 棋子组件日志（输出太多）
            testing: false,             // 测试模块日志（仅开发时需要）
            vibration: false,           // 振动系统日志（不重要）
            enableAllLogs: false
        };

        const LogManager = {
            gameManager: {
                log: (message, ...args) => {
                    if (LogConfig.enableAllLogs || LogConfig.gameManager) {
                        logToConsole(`🎮 [GameManager] ${message}`, ...args);
                    }
                },
                warn: (message, ...args) => {
                    if (LogConfig.enableAllLogs || LogConfig.gameManager) {
                        logToConsole(`🎮 [GameManager] WARNING: ${message}`, ...args);
                    }
                },
                error: (message, ...args) => {
                    if (LogConfig.enableAllLogs || LogConfig.enableErrorLogs) {
                        logToConsole(`🎮 [GameManager] ERROR: ${message}`, ...args);
                    }
                }
            },
            connectionSystem: {
                log: (message, ...args) => {
                    if (LogConfig.enableAllLogs || LogConfig.connectionSystem) {
                        logToConsole(`🔗 [Connection] ${message}`, ...args);
                    }
                }
            },
            selectionLogic: {
                log: (message, ...args) => {
                    if (LogConfig.enableAllLogs || LogConfig.selectionLogic) {
                        logToConsole(`👆 [Selection] ${message}`, ...args);
                    }
                }
            },
            audioManager: {
                log: (message, ...args) => {
                    if (LogConfig.enableAllLogs || LogConfig.audioManager) {
                        logToConsole(`🎵 [Audio] ${message}`, ...args);
                    }
                }
            },
            shuffleAlgorithm: {
                log: (message, ...args) => {
                    if (LogConfig.enableAllLogs || LogConfig.shuffleAlgorithm) {
                        logToConsole(`🔄 [Shuffle] ${message}`, ...args);
                    }
                }
            },
            animationState: {
                log: (message, ...args) => {
                    if (LogConfig.enableAllLogs || LogConfig.animationState) {
                        logToConsole(`🎬 [Animation] ${message}`, ...args);
                    }
                }
            },
            menuManager: {
                log: (message, ...args) => {
                    if (LogConfig.enableAllLogs || LogConfig.menuManager) {
                        logToConsole(`📱 [Menu] ${message}`, ...args);
                    }
                }
            },
            toastSystem: {
                log: (message, ...args) => {
                    if (LogConfig.enableAllLogs || LogConfig.toastSystem) {
                        logToConsole(`🍞 [Toast] ${message}`, ...args);
                    }
                }
            },
            loading: {
                log: (message, ...args) => {
                    if (LogConfig.enableAllLogs || LogConfig.loading) {
                        logToConsole(`📦 [Loading] ${message}`, ...args);
                    }
                }
            },
            cellComponent: {
                log: (message, ...args) => {
                    if (LogConfig.enableAllLogs || LogConfig.cellComponent) {
                        logToConsole(`🎯 [Cell] ${message}`, ...args);
                    }
                }
            },
            testing: {
                log: (message, ...args) => {
                    if (LogConfig.enableAllLogs || LogConfig.testing) {
                        logToConsole(`🧪 [Testing] ${message}`, ...args);
                    }
                }
            },
            vibration: {
                log: (message, ...args) => {
                    if (LogConfig.enableAllLogs || LogConfig.vibration) {
                        logToConsole(`📳 [Vibration] ${message}`, ...args);
                    }
                }
            }
        };

        function logToConsole(message, ...args) {
            const output = document.getElementById('console-output');
            const timestamp = new Date().toLocaleTimeString();
            const logLine = `[${timestamp}] ${message} ${args.length > 0 ? JSON.stringify(args) : ''}\n`;
            output.textContent += logLine;
            output.scrollTop = output.scrollHeight;
        }

        function updateConfigDisplay() {
            const display = document.getElementById('config-display');
            let html = '';
            for (const [key, value] of Object.entries(LogConfig)) {
                const status = value ? '✅' : '❌';
                html += `<div class="config-item">${status} ${key}: ${value}</div>`;
            }
            display.innerHTML = html;
        }

        function testGameManager() {
            LogManager.gameManager.log("游戏管理器初始化");
            LogManager.gameManager.warn("游戏管理器警告测试");
            LogManager.gameManager.error("游戏管理器错误测试");
        }

        function testConnectionSystem() {
            LogManager.connectionSystem.log("连接系统测试");
            LogManager.connectionSystem.log("寻找连接路径");
            LogManager.connectionSystem.log("连接验证成功");
        }

        function testSelectionLogic() {
            LogManager.selectionLogic.log("选择逻辑测试");
            LogManager.selectionLogic.log("棋子选中");
            LogManager.selectionLogic.log("取消选中");
        }

        function testAudioManager() {
            LogManager.audioManager.log("音频管理器测试");
            LogManager.audioManager.log("播放背景音乐");
            LogManager.audioManager.log("播放音效");
        }

        function testShuffleAlgorithm() {
            LogManager.shuffleAlgorithm.log("洗牌算法测试");
            LogManager.shuffleAlgorithm.log("开始洗牌");
            LogManager.shuffleAlgorithm.log("洗牌完成");
        }

        function testAnimationState() {
            LogManager.animationState.log("动画状态测试");
            LogManager.animationState.log("动画开始");
            LogManager.animationState.log("动画结束");
        }

        function testMenuManager() {
            LogManager.menuManager.log("菜单管理器测试");
            LogManager.menuManager.log("显示菜单");
            LogManager.menuManager.log("隐藏菜单");
        }

        function testToastSystem() {
            LogManager.toastSystem.log("Toast系统测试");
            LogManager.toastSystem.log("显示提示");
            LogManager.toastSystem.log("隐藏提示");
        }

        function testLoading() {
            LogManager.loading.log("加载界面测试");
            LogManager.loading.log("开始加载");
            LogManager.loading.log("加载完成");
        }

        function testCellComponent() {
            LogManager.cellComponent.log("棋子组件测试");
            LogManager.cellComponent.log("棋子创建");
            LogManager.cellComponent.log("棋子销毁");
        }

        function testTestingModule() {
            LogManager.testing.log("测试模块测试");
            LogManager.testing.log("开始测试");
            LogManager.testing.log("测试完成");
        }

        function testVibration() {
            LogManager.vibration.log("振动系统测试");
            LogManager.vibration.log("开始振动");
            LogManager.vibration.log("停止振动");
        }

        function toggleAllLogs() {
            LogConfig.enableAllLogs = !LogConfig.enableAllLogs;
            logToConsole(`🔧 所有日志已${LogConfig.enableAllLogs ? '启用' : '禁用'}`);
            updateConfigDisplay();
        }

        function toggleErrorLogs() {
            LogConfig.enableErrorLogs = !LogConfig.enableErrorLogs;
            logToConsole(`🔧 错误日志已${LogConfig.enableErrorLogs ? '启用' : '禁用'}`);
            updateConfigDisplay();
        }

        function clearConsole() {
            document.getElementById('console-output').textContent = '';
        }

        function enableDebugMode() {
            // 启用所有日志用于调试
            Object.keys(LogConfig).forEach(key => {
                if (key !== 'enableAllLogs' && key !== 'enableErrorLogs') {
                    LogConfig[key] = true;
                }
            });
            LogConfig.enableAllLogs = true;
            logToConsole('🔧 已切换到调试模式 - 启用所有日志');
            updateConfigDisplay();
        }

        function enableProductionMode() {
            // 只保留错误日志
            Object.keys(LogConfig).forEach(key => {
                if (key !== 'enableErrorLogs') {
                    LogConfig[key] = false;
                }
            });
            LogConfig.enableErrorLogs = true;
            logToConsole('🔧 已切换到生产模式 - 只保留错误日志');
            updateConfigDisplay();
        }

        // 初始化显示
        updateConfigDisplay();
        logToConsole('🚀 日志管理器测试页面已加载（简化配置）');
        logToConsole('💡 当前只启用重要日志：无解检测、Toast提示、加载状态、错误信息');
    </script>
</body>
</html>
