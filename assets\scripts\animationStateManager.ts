import { Node, Vec3, tween } from 'cc';

/**
 * 动画状态枚举
 * 定义棋子可能的动画状态
 */
export enum AnimationState {
    NORMAL = "normal",           // 正常状态：缩放 1.0，默认显示
    SELECTED = "selected",       // 选中状态：缩放 1.1，用户点击后的放大效果
    FAILURE = "failure",         // 失败状态：闪烁动画，连接失败时的反馈
    DESTROYING = "destroying",   // 消除状态：缩放到 0，棋子被消除时的动画
    HINT = "hint"               // 提示状态：高亮闪烁，提示可连接的棋子
}

/**
 * 棋子动画状态信息
 * 记录每个棋子的动画状态和相关信息
 */
interface CellAnimationInfo {
    node: Node;                  // 棋子节点引用
    currentState: AnimationState; // 当前动画状态
    targetState: AnimationState;  // 目标动画状态
    isAnimating: boolean;         // 是否正在播放动画
    animationId: number;          // 动画唯一ID，用于防止动画冲突
}

/**
 * 动画状态管理器
 *
 * 核心功能：
 * 1. 统一管理所有棋子的动画状态
 * 2. 防止动画冲突和状态不一致
 * 3. 提供强制清理机制确保状态正确
 *
 * 设计原则：
 * - 每个棋子同时只能有一个活跃动画
 * - 使用动画ID防止过期动画影响状态
 * - 提供多层清理机制确保状态一致性
 */
export class AnimationStateManager {

    // ==================== 单例模式 ====================
    private static instance: AnimationStateManager = null;

    // ==================== 状态管理 ====================
    /** 存储所有棋子的动画状态信息 */
    private cellStates: Map<Node, CellAnimationInfo> = new Map();
    /** 动画ID计数器，用于生成唯一的动画标识 */
    private animationCounter: number = 0;

    // ==================== 动画配置常量 ====================
    /** 正常状态的缩放值 */
    private readonly NORMAL_SCALE = new Vec3(1.0, 1.0, 1.0);
    /** 选中状态的缩放值 */
    private readonly SELECTED_SCALE = new Vec3(1.1, 1.1, 1.0);
    /** 动画持续时间（秒） */
    private readonly ANIMATION_DURATION = 0.15;
    
    // ==================== 单例实现 ====================

    constructor() {
        if (AnimationStateManager.instance === null) {
            AnimationStateManager.instance = this;
        }
        return AnimationStateManager.instance;
    }

    /**
     * 获取动画状态管理器的单例实例
     * @returns 动画状态管理器实例
     */
    public static getInstance(): AnimationStateManager {
        if (AnimationStateManager.instance === null) {
            AnimationStateManager.instance = new AnimationStateManager();
        }
        return AnimationStateManager.instance;
    }
    
    // ==================== 公共API方法 ====================

    /**
     * 设置棋子为选中状态
     * 棋子会放大到1.1倍，表示被用户选中
     * @param cellNode 要设置的棋子节点
     */
    public setSelected(cellNode: Node): void {
        this.transitionToState(cellNode, AnimationState.SELECTED);
    }

    /**
     * 设置棋子为正常状态
     * 棋子会恢复到1.0倍大小，表示未选中状态
     * @param cellNode 要设置的棋子节点
     */
    public setNormal(cellNode: Node): void {
        this.transitionToState(cellNode, AnimationState.NORMAL);
    }

    /**
     * 播放失败动画
     * 棋子会播放闪烁效果，表示连接失败
     * @param cellNode 要播放动画的棋子节点
     * @param callback 动画完成后的回调函数
     */
    public playFailureAnimation(cellNode: Node, callback?: () => void): void {
        this.transitionToState(cellNode, AnimationState.FAILURE, callback);
    }

    /**
     * 播放消除动画
     * 棋子会缩放到0并隐藏，表示被消除
     * @param cellNode 要播放动画的棋子节点
     * @param callback 动画完成后的回调函数
     */
    public playDestroyAnimation(cellNode: Node, callback?: () => void): void {
        this.transitionToState(cellNode, AnimationState.DESTROYING, callback);
    }

    /**
     * 播放提示动画
     * 棋子会高亮闪烁，提示用户这是可连接的棋子
     * @param cellNode 要播放动画的棋子节点
     * @param callback 动画完成后的回调函数
     */
    public playHintAnimation(cellNode: Node, callback?: () => void): void {
        this.transitionToState(cellNode, AnimationState.HINT, callback);
    }
    
    // ==================== 强制重置方法 ====================

    /**
     * 强制重置棋子到正常状态（立即生效，无动画过渡）
     * 用于紧急情况下的状态清理，确保棋子处于正确状态
     * @param cellNode 要重置的棋子节点
     */
    public forceResetToNormal(cellNode: Node): void {
        // 停止当前所有动画
        this.stopAnimation(cellNode);

        // 创建新的状态信息
        const info: CellAnimationInfo = {
            node: cellNode,
            currentState: AnimationState.NORMAL,
            targetState: AnimationState.NORMAL,
            isAnimating: false,
            animationId: 0
        };

        // 更新状态记录
        this.cellStates.set(cellNode, info);
        // 立即设置为正常缩放
        cellNode.scale = this.NORMAL_SCALE.clone();


    }

    /**
     * 批量强制重置多个棋子到正常状态
     * @param cellNodes 要重置的棋子节点数组
     */
    public forceResetMultiple(cellNodes: Node[]): void {
        cellNodes.forEach(cellNode => {
            if (cellNode && cellNode.active) {
                this.forceResetToNormal(cellNode);
            }
        });
    }
    
    // ==================== 状态查询方法 ====================

    /**
     * 获取棋子当前的动画状态
     * @param cellNode 要查询的棋子节点
     * @returns 当前动画状态，如果未记录则返回NORMAL
     */
    public getCurrentState(cellNode: Node): AnimationState {
        const info = this.cellStates.get(cellNode);
        return info ? info.currentState : AnimationState.NORMAL;
    }

    /**
     * 检查棋子是否正在播放动画
     * @param cellNode 要检查的棋子节点
     * @returns 是否正在动画中
     */
    public isAnimating(cellNode: Node): boolean {
        const info = this.cellStates.get(cellNode);
        return info ? info.isAnimating : false;
    }

    // ==================== 动画控制方法 ====================

    /**
     * 停止棋子的所有动画
     * @param cellNode 要停止动画的棋子节点
     */
    public stopAnimation(cellNode: Node): void {
        const info = this.cellStates.get(cellNode);
        if (info && info.isAnimating) {
            tween(cellNode).stop();
            info.isAnimating = false;

        }
    }

    // ==================== 批量清理方法 ====================

    /**
     * 清理所有动画状态并重置所有棋子
     * 用于游戏重置或紧急状态清理
     */
    public clearAllStates(): void {
        this.cellStates.forEach((_, cellNode) => {
            this.stopAnimation(cellNode);
            this.forceResetToNormal(cellNode);
        });
        this.cellStates.clear();

    }

    /**
     * 清理异常状态的棋子（智能清理）
     *
     * 清理条件：
     * 1. 处于失败状态或正在播放失败动画的棋子
     * 2. 缩放值异常的棋子（不是1.0也不是1.1）
     * 3. 正在播放非选中相关动画的棋子
     *
     * 保留条件：
     * - 正常状态的棋子（缩放1.0）
     * - 选中状态的棋子（缩放1.1）
     *
     * 用于快速点击时清理失败动画等异常状态，确保游戏视觉效果正确
     */
    public clearAbnormalStates(): void {
        const cellsToReset: Node[] = [];

        this.cellStates.forEach((info, cellNode) => {
            // 检查失败状态：当前是失败状态或正在播放失败动画
            const isFailureState = info.currentState === AnimationState.FAILURE ||
                                   (info.isAnimating && info.targetState === AnimationState.FAILURE);

            // 检查提示状态：当前是提示状态或正在播放提示动画
            const isHintState = info.currentState === AnimationState.HINT ||
                               (info.isAnimating && info.targetState === AnimationState.HINT);

            // 检查缩放异常：不是正常(1.0)也不是选中(1.1)状态
            const isAbnormalScale = cellNode.scale.x !== 1.0 && cellNode.scale.x !== 1.1;

            // 检查动画异常：正在播放非选中相关的动画
            const isAbnormalAnimation = info.isAnimating &&
                                       info.currentState !== AnimationState.SELECTED &&
                                       info.targetState !== AnimationState.SELECTED;

            // 满足任一条件就需要重置（但保留提示状态，除非用户主动点击）
            const shouldReset = isFailureState || isAbnormalScale || isAbnormalAnimation;

            if (shouldReset) {
                cellsToReset.push(cellNode);
            }
        });

        // 批量重置所有异常棋子
        cellsToReset.forEach(cellNode => {
            this.stopAnimation(cellNode);
            this.forceResetToNormal(cellNode);
        });

        // 清理完成，无需详细日志
    }

    /**
     * 强力清理所有异常缩放的棋子（终极修复方法）
     *
     * 功能：
     * - 直接遍历棋盘上的所有棋子
     * - 检查缩放值是否异常
     * - 立即重置异常棋子到正常状态
     *
     * 使用场景：
     * - 当智能清理无法完全解决问题时
     * - 作为最后的保障机制
     * - 确保没有棋子停留在错误的缩放状态
     *
     * @param gridNode 棋盘节点，包含所有棋子
     */
    public forceCleanAllAbnormalScales(gridNode: Node): void {
        if (!gridNode || !gridNode.children) {
            console.warn("⚠️ 棋盘节点无效，跳过强力清理");
            return;
        }

        let resetCount = 0;
        gridNode.children.forEach(cellNode => {
            if (cellNode && cellNode.active) {
                const scale = cellNode.scale.x;

                // 检查缩放是否异常：不是正常(1.0)也不是选中(1.1)状态
                if (scale !== 1.0 && scale !== 1.1) {
                    // 立即停止动画并重置
                    tween(cellNode).stop();
                    cellNode.scale = this.NORMAL_SCALE.clone();
                    resetCount++;
                }
            }
        });

        // 强力清理完成，无需详细日志
    }

    /**
     * 清理所有提示状态的棋子
     * 用于用户点击后清除之前的提示效果
     */
    public clearHintStates(): void {
        const hintCells: Node[] = [];

        this.cellStates.forEach((info, cellNode) => {
            if (info.currentState === AnimationState.HINT ||
                (info.isAnimating && info.targetState === AnimationState.HINT)) {
                hintCells.push(cellNode);
            }
        });

        hintCells.forEach(cellNode => {
            this.stopAnimation(cellNode);
            this.forceResetToNormal(cellNode);
        });

        // 提示状态清理完成，无需详细日志
    }
    
    // ==================== 核心状态转换方法 ====================

    /**
     * 状态转换核心方法
     *
     * 功能：
     * 1. 管理棋子从当前状态到目标状态的转换
     * 2. 确保动画不冲突（使用动画ID机制）
     * 3. 处理状态信息的创建和更新
     *
     * 防冲突机制：
     * - 每个动画都有唯一ID
     * - 新动画开始前停止旧动画
     * - 只有匹配ID的动画才能更新最终状态
     *
     * @param cellNode 要转换状态的棋子节点
     * @param targetState 目标动画状态
     * @param callback 动画完成后的回调函数
     */
    private transitionToState(cellNode: Node, targetState: AnimationState, callback?: () => void): void {
        // 安全检查：节点是否有效
        if (!cellNode || !cellNode.active) {
            callback?.();
            return;
        }

        // 获取或创建棋子的状态信息
        let info = this.cellStates.get(cellNode);
        if (!info) {
            // 首次记录该棋子，创建默认状态信息
            info = {
                node: cellNode,
                currentState: AnimationState.NORMAL,
                targetState: AnimationState.NORMAL,
                isAnimating: false,
                animationId: 0
            };
            this.cellStates.set(cellNode, info);
        }

        // 优化：如果已经是目标状态且没在动画，直接返回
        if (info.currentState === targetState && !info.isAnimating) {
            callback?.();
            return;
        }

        // 防冲突：停止当前正在播放的动画
        if (info.isAnimating) {
            tween(cellNode).stop();
        }

        // 更新状态信息，准备开始新动画
        info.targetState = targetState;
        info.isAnimating = true;
        info.animationId = ++this.animationCounter; // 生成唯一动画ID

        const currentAnimationId = info.animationId;



        // 根据目标状态执行对应的动画
        switch (targetState) {
            case AnimationState.NORMAL:
                this.animateToNormal(cellNode, info, currentAnimationId, callback);
                break;
            case AnimationState.SELECTED:
                this.animateToSelected(cellNode, info, currentAnimationId, callback);
                break;
            case AnimationState.FAILURE:
                this.animateFailure(cellNode, info, currentAnimationId, callback);
                break;
            case AnimationState.DESTROYING:
                this.animateDestroy(cellNode, info, currentAnimationId, callback);
                break;
            case AnimationState.HINT:
                this.animateHint(cellNode, info, currentAnimationId, callback);
                break;
        }
    }

    // ==================== 具体动画实现方法 ====================

    /**
     * 动画到正常状态
     * 棋子平滑缩放到1.0倍大小
     * @param cellNode 棋子节点
     * @param info 状态信息
     * @param animationId 动画ID（用于防冲突）
     * @param callback 完成回调
     */
    private animateToNormal(cellNode: Node, info: CellAnimationInfo, animationId: number, callback?: () => void): void {
        tween(cellNode)
            .to(this.ANIMATION_DURATION, { scale: this.NORMAL_SCALE })
            .call(() => {
                // 防冲突检查：只有当前动画才能更新状态
                if (info.animationId === animationId) {
                    info.currentState = AnimationState.NORMAL;
                    info.isAnimating = false;

                }
                callback?.();
            })
            .start();
    }

    /**
     * 动画到选中状态
     * 棋子平滑放大到1.1倍大小，表示被选中
     * @param cellNode 棋子节点
     * @param info 状态信息
     * @param animationId 动画ID（用于防冲突）
     * @param callback 完成回调
     */
    private animateToSelected(cellNode: Node, info: CellAnimationInfo, animationId: number, callback?: () => void): void {
        tween(cellNode)
            .to(this.ANIMATION_DURATION, { scale: this.SELECTED_SCALE })
            .call(() => {
                // 防冲突检查：只有当前动画才能更新状态
                if (info.animationId === animationId) {
                    info.currentState = AnimationState.SELECTED;
                    info.isAnimating = false;

                }
                callback?.();
            })
            .start();
    }
    
    /**
     * 失败动画（闪烁效果）
     *
     * 动画效果：
     * 1. 快速放大到1.15倍（突出显示）
     * 2. 缩小到0.95倍（回弹效果）
     * 3. 再次放大到1.1倍（强调失败）
     * 4. 轻微缩小到0.98倍（细微调整）
     * 5. 最终恢复到1.0倍（正常状态）
     *
     * 总时长：0.5秒，给用户明确的失败反馈
     *
     * @param cellNode 棋子节点
     * @param info 状态信息
     * @param animationId 动画ID（用于防冲突）
     * @param callback 完成回调
     */
    private animateFailure(cellNode: Node, info: CellAnimationInfo, animationId: number, callback?: () => void): void {
        const originalScale = cellNode.scale.clone();

        tween(cellNode)
            // 阶段1：突出显示（放大）
            .to(0.1, { scale: new Vec3(originalScale.x * 1.15, originalScale.y * 1.15, originalScale.z) })
            // 阶段2：回弹效果（缩小）
            .to(0.1, { scale: new Vec3(originalScale.x * 0.95, originalScale.y * 0.95, originalScale.z) })
            // 阶段3：再次强调（放大）
            .to(0.1, { scale: new Vec3(originalScale.x * 1.1, originalScale.y * 1.1, originalScale.z) })
            // 阶段4：细微调整（轻微缩小）
            .to(0.1, { scale: new Vec3(originalScale.x * 0.98, originalScale.y * 0.98, originalScale.z) })
            // 阶段5：恢复正常（最终状态）
            .to(0.1, { scale: this.NORMAL_SCALE })
            .call(() => {
                // 防冲突检查：只有当前动画才能更新状态
                if (info.animationId === animationId) {
                    info.currentState = AnimationState.NORMAL;
                    info.isAnimating = false;

                }
                callback?.();
            })
            .start();
    }

    /**
     * 消除动画
     *
     * 动画效果：
     * - 棋子缩放到0，产生消失效果
     * - 动画完成后隐藏节点
     *
     * 时长：0.2秒，快速消除不影响游戏节奏
     *
     * @param cellNode 棋子节点
     * @param info 状态信息
     * @param animationId 动画ID（用于防冲突）
     * @param callback 完成回调
     */
    private animateDestroy(cellNode: Node, info: CellAnimationInfo, animationId: number, callback?: () => void): void {
        tween(cellNode)
            .to(0.2, { scale: new Vec3(0, 0, 1) })
            .call(() => {
                // 防冲突检查：只有当前动画才能更新状态
                if (info.animationId === animationId) {
                    cellNode.active = false; // 隐藏节点
                    info.currentState = AnimationState.DESTROYING;
                    info.isAnimating = false;

                }
                callback?.();
            })
            .start();
    }

    /**
     * 提示动画（高亮闪烁效果）
     *
     * 动画效果：
     * - 棋子会进行高亮闪烁，吸引用户注意
     * - 循环3次闪烁后自动停止
     * - 闪烁期间棋子会在正常大小和稍大之间切换
     *
     * 时长：1.5秒，给用户足够的提示时间
     *
     * @param cellNode 棋子节点
     * @param info 状态信息
     * @param animationId 动画ID（用于防冲突）
     * @param callback 完成回调
     */
    private animateHint(cellNode: Node, info: CellAnimationInfo, animationId: number, callback?: () => void): void {
        const originalScale = cellNode.scale.clone();
        const hintScale = new Vec3(originalScale.x * 1.2, originalScale.y * 1.2, originalScale.z);

        tween(cellNode)
            // 第一次闪烁
            .to(0.2, { scale: hintScale })
            .to(0.2, { scale: originalScale })
            // 第二次闪烁
            .to(0.2, { scale: hintScale })
            .to(0.2, { scale: originalScale })
            // 第三次闪烁
            .to(0.2, { scale: hintScale })
            .to(0.2, { scale: originalScale })
            // 最终恢复正常
            .to(0.3, { scale: this.NORMAL_SCALE })
            .call(() => {
                // 防冲突检查：只有当前动画才能更新状态
                if (info.animationId === animationId) {
                    info.currentState = AnimationState.NORMAL;
                    info.isAnimating = false;

                }
                callback?.();
            })
            .start();
    }

    // ==================== 调试和工具方法 ====================

    /**
     * 获取动画系统的调试信息
     *
     * 返回信息包括：
     * - 总棋子数量
     * - 正在动画的棋子数量
     * - 各种状态的棋子统计
     *
     * @returns 格式化的调试信息字符串
     */
    public getDebugInfo(): string {
        const states = Array.from(this.cellStates.values());
        const animatingCount = states.filter(info => info.isAnimating).length;

        // 统计各种状态的棋子数量
        const stateCount = {
            normal: states.filter(info => info.currentState === AnimationState.NORMAL).length,
            selected: states.filter(info => info.currentState === AnimationState.SELECTED).length,
            failure: states.filter(info => info.currentState === AnimationState.FAILURE).length,
            destroying: states.filter(info => info.currentState === AnimationState.DESTROYING).length
        };

        return `动画系统状态: 总计${states.length}个棋子, ${animatingCount}个动画中 ` +
               `[正常:${stateCount.normal} 选中:${stateCount.selected} 失败:${stateCount.failure} 消除:${stateCount.destroying}]`;
    }

    /**
     * 打印详细的调试信息到控制台
     * 用于开发和调试时查看系统状态
     */
    public printDetailedDebugInfo(): void {
        const debugInfo = {
            animationCounter: this.animationCounter,
            trackedCells: this.cellStates.size,
            cells: Array.from(this.cellStates.entries()).map(([cellNode, info]) => ({
                name: cellNode.name,
                currentState: info.currentState,
                targetState: info.targetState,
                scale: cellNode.scale.x.toFixed(2),
                isAnimating: info.isAnimating,
                animationId: info.animationId
            }))
        };
        console.log("动画状态管理器详细信息:", debugInfo);
    }
}
