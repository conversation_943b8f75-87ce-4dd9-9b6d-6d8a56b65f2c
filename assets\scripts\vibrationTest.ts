import { _decorator, Component, Node, Button, Label, tween, Vec3 } from 'cc';
const { ccclass, property } = _decorator;

/**
 * 震动功能测试脚本
 * 用于测试设备震动和视觉震动效果
 */
@ccclass('vibrationTest')
export class vibrationTest extends Component {
    
    @property({ type: Node, displayName: "测试按钮容器" })
    buttonContainer: Node = null!;
    
    @property({ type: Label, displayName: "状态显示标签" })
    statusLabel: Label = null!;
    
    onLoad() {
        this.createTestButtons();
        this.updateStatus("震动测试准备就绪");
    }
    
    /**
     * 创建测试按钮
     */
    private createTestButtons() {
        if (!this.buttonContainer) {
            console.warn("按钮容器未设置");
            return;
        }
        
        this.createButton("测试设备震动", () => this.testDeviceVibration());
        this.createButton("测试视觉震动", () => this.testVisualShake());
        this.createButton("测试连击震动", () => this.testMultipleVibrations());
        this.createButton("检查震动支持", () => this.checkVibrationSupport());
    }
    
    /**
     * 创建单个测试按钮
     */
    private createButton(text: string, callback: () => void) {
        const buttonNode = new Node(text);
        buttonNode.parent = this.buttonContainer;
        
        const button = buttonNode.addComponent(Button);
        const label = buttonNode.addComponent(Label);
        
        label.string = text;
        label.fontSize = 20;
        
        button.node.on(Button.EventType.CLICK, () => {
            callback();
        }, this);
    }
    
    /**
     * 更新状态显示
     */
    private updateStatus(message: string) {
        if (this.statusLabel) {
            this.statusLabel.string = message;
        }
    }
    
    // ==================== 测试方法 ====================
    
    /**
     * 测试设备震动
     */
    private testDeviceVibration() {
        this.updateStatus("测试设备震动...");
        
        if (navigator && navigator.vibrate) {
            // 播放震动模式：震动100ms，停止50ms，再震动100ms
            navigator.vibrate([100, 50, 100]);
            this.updateStatus("✓ 设备震动已触发");
        } else {
            this.updateStatus("❌ 设备不支持震动功能");
        }
    }
    
    /**
     * 测试视觉震动
     */
    private testVisualShake() {
        this.updateStatus("测试视觉震动...");

        const originalPosition = this.node.position.clone();
        const shakeIntensity = 10; // 更明显的震动强度

        // 停止之前的动画
        tween(this.node).stop();

        // 创建震动动画
        tween(this.node)
            .to(0.05, {
                position: originalPosition.add3f(shakeIntensity, 0, 0)
            })
            .to(0.05, {
                position: originalPosition.add3f(-shakeIntensity, 0, 0)
            })
            .to(0.05, {
                position: originalPosition.add3f(shakeIntensity, 0, 0)
            })
            .to(0.05, {
                position: originalPosition.add3f(-shakeIntensity, 0, 0)
            })
            .to(0.1, {
                position: originalPosition
            })
            .call(() => {
                this.updateStatus("✓ 视觉震动效果完成");
            })
            .start();
    }
    
    /**
     * 测试连击震动
     */
    private testMultipleVibrations() {
        this.updateStatus("测试连击震动...");
        
        let count = 0;
        const maxCount = 3;
        
        const vibrationInterval = setInterval(() => {
            count++;
            
            if (navigator && navigator.vibrate) {
                navigator.vibrate(100);
            }
            
            this.updateStatus(`连击震动 ${count}/${maxCount}`);
            
            if (count >= maxCount) {
                clearInterval(vibrationInterval);
                this.updateStatus("✓ 连击震动测试完成");
            }
        }, 200);
    }
    
    /**
     * 检查震动支持
     */
    private checkVibrationSupport() {
        let supportInfo = "震动支持检查结果:\n";
        
        // 检查基本震动API
        if (navigator && navigator.vibrate) {
            supportInfo += "✓ 支持基本震动API\n";
        } else {
            supportInfo += "❌ 不支持基本震动API\n";
        }
        
        // 检查用户代理
        const userAgent = navigator.userAgent;
        if (userAgent.includes('Mobile') || userAgent.includes('Android') || userAgent.includes('iPhone')) {
            supportInfo += "✓ 检测到移动设备\n";
        } else {
            supportInfo += "⚠️ 可能不是移动设备\n";
        }
        
        // 检查是否在HTTPS环境
        if (location.protocol === 'https:' || location.hostname === 'localhost') {
            supportInfo += "✓ 安全环境（HTTPS或localhost）\n";
        } else {
            supportInfo += "⚠️ 非安全环境，可能影响震动功能\n";
        }
        
        this.updateStatus(supportInfo);
    }
}
