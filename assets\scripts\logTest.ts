import { _decorator, Component } from 'cc';
const { ccclass } = _decorator;
import { LogManager } from './logManager';
import { LogConfig } from './config';

/**
 * 日志管理系统测试脚本
 * 用于验证日志管理功能是否正常工作
 */
@ccclass('logTest')
export class logTest extends Component {

    onLoad() {
        this.testLogManager();
    }

    /**
     * 测试日志管理器功能
     */
    private testLogManager() {
        console.log("=== 开始测试日志管理系统 ===");
        
        // 测试各个模块的日志输出
        console.log("\n1. 测试各模块日志输出（当前配置下）:");
        
        LogManager.gameManager.log("游戏管理器日志测试");
        LogManager.connectionSystem.log("连接系统日志测试");
        LogManager.selectionLogic.log("选择逻辑日志测试");
        LogManager.animationState.log("动画状态日志测试");
        LogManager.deadlockDetector.log("无解检测日志测试");
        LogManager.shuffleAlgorithm.log("洗牌算法日志测试");
        LogManager.menuManager.log("菜单管理器日志测试");
        LogManager.toastSystem.log("Toast系统日志测试");
        LogManager.audioManager.log("音频管理器日志测试");
        LogManager.loading.log("加载界面日志测试");
        LogManager.cellComponent.log("棋子组件日志测试");
        LogManager.testing.log("测试模块日志测试");
        LogManager.vibration.log("振动系统日志测试");

        // 测试警告和错误日志
        console.log("\n2. 测试警告和错误日志:");
        LogManager.gameManager.warn("这是一个警告日志测试");
        LogManager.gameManager.error("这是一个错误日志测试");

        // 显示当前配置状态
        console.log("\n3. 当前日志配置状态:");
        console.log("gameManager:", LogConfig.gameManager);
        console.log("connectionSystem:", LogConfig.connectionSystem);
        console.log("loading:", LogConfig.loading);
        console.log("cellComponent:", LogConfig.cellComponent);
        console.log("testing:", LogConfig.testing);
        console.log("vibration:", LogConfig.vibration);
        console.log("enableAllLogs:", LogConfig.enableAllLogs);
        console.log("enableErrorLogs:", LogConfig.enableErrorLogs);

        console.log("\n=== 日志管理系统测试完成 ===");
        console.log("注意：只有配置为 true 的模块才会显示日志输出");
        console.log("错误日志应该始终显示（enableErrorLogs: true）");
    }

    /**
     * 启用所有日志进行测试
     */
    public enableAllLogsForTesting() {
        console.log("=== 启用所有日志进行测试 ===");
        
        // 临时启用所有日志
        const originalConfig = { ...LogConfig };
        
        // 修改配置（注意：这只是临时修改，不会持久化）
        Object.keys(LogConfig).forEach(key => {
            if (key !== 'enableErrorLogs') {
                (LogConfig as any)[key] = true;
            }
        });

        // 重新测试
        this.testLogManager();

        // 恢复原始配置
        Object.assign(LogConfig, originalConfig);
        
        console.log("=== 已恢复原始日志配置 ===");
    }
}
