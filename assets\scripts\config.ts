//设备宽高
export const deviceWidth: number = 720;
export const deviceHeight: number = 1280;

// //棋盘宽高
// export const gridWidth: number = 700;
// export const gridHeight: number = 700;

//棋子间距（控制棋子之间的距离）
export const cellSpacing: number = 75;

//棋子默认大小（可在编辑器中覆盖）
export const defaultCellSize: number = 100;

//水果种类最小值
export const minFruitType: number = 1;
//水果种类最大值
export const maxFruitType: number = 11;

//关卡数=第几关
export const levelNum: number = 2;

// ==================== 日志控制配置 ====================

/**
 * 日志控制开关
 * 设置为 false 可以关闭对应模块的日志输出
 */
export const LogConfig = {
    // 游戏核心模块 - 只保留关键日志
    gameManager: false,          // 游戏管理器日志（关闭，输出太多）
    connectionSystem: false,     // 连接系统日志（关闭，输出太多）
    selectionLogic: false,       // 选择逻辑日志（关闭，输出太多）
    animationState: false,       // 动画状态日志（关闭，输出太多）

    // 检测和算法模块 - 保留重要提示
    deadlockDetector: true,      // 无解检测日志（保留，重要提示）
    shuffleAlgorithm: false,     // 洗牌算法详细日志（关闭，太详细）

    // UI和交互模块 - 保留用户可见的操作
    menuManager: false,          // 菜单管理器日志（关闭，减少输出）
    toastSystem: true,           // Toast提示系统日志（保留，用户可见）

    // 音频模块
    audioManager: false,         // 音频管理器日志（关闭）

    // 界面和组件模块 - 只保留加载相关
    loading: true,               // 加载界面日志（保留，重要状态）
    cellComponent: false,        // 棋子组件日志（关闭，输出太多）

    // 测试和调试模块 - 开发时使用
    testing: false,              // 测试模块日志（关闭，仅开发时需要）
    debugInfo: false,            // 调试信息输出（关闭）
    performance: false,          // 性能监控日志（关闭）
    vibration: false,            // 振动系统日志（关闭）

    // 全局开关
    enableAllLogs: false,        // 设置为 true 时启用所有日志
    enableErrorLogs: true,       // 错误日志始终启用（重要！）
};